"use client"

import { useRole } from "@/hooks/use-role"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getAllChains } from "@/lib/db"
import { useRouter } from "next/navigation"

export default function RoleSwitcher() {
  const { role, setRole, currentChainId, setCurrentChainId } = useRole()
  const chains = getAllChains()
  const router = useRouter()

  return (
    <div className="flex items-center gap-2">
      <Select value={role} onValueChange={(v) => setRole(v as any)}>
        <SelectTrigger className="w-[120px] bg-white/5 border-white/10 text-white">
          <SelectValue placeholder="角色" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="hq">总台</SelectItem>
          <SelectItem value="chain">院线</SelectItem>
        </SelectContent>
      </Select>

      {role === "chain" ? (
        <Select
          value={currentChainId ?? undefined}
          onValueChange={(v) => {
            setCurrentChainId(v)
            router.push(`/cinema/${v}`)
          }}
        >
          <SelectTrigger className="w-[200px] bg-white/5 border-white/10 text-white">
            <SelectValue placeholder="选择院线" />
          </SelectTrigger>
          <SelectContent>
            {chains.map((c) => (
              <SelectItem key={c.id} value={c.id}>
                {c.city} - {c.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : null}
    </div>
  )
}
