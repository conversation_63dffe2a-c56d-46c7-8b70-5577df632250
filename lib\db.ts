"use client"

import type { AutomationRule, Chain, Floor, Room, TrendSeries } from "./types"
import { nanoid } from "nanoid"
import { seedChains } from "@/seed/seed-data"

// localStorage 持久化（演示用途）
const STORE_KEY = "cinema_store_v1"

type Store = {
  chains: Chain[]
  trends: Record<string, { temperature: TrendSeries[]; humidity: TrendSeries[]; presence: TrendSeries[] }>
  // trends key: `${chainId}-${floorId}-${roomId}`
  lastUpdated: number // 用于简单保留策略（最长1年）
}

function load(): Store {
  const raw = typeof window !== "undefined" ? localStorage.getItem(STORE_KEY) : null
  if (raw) {
    try {
      const obj = JSON.parse(raw) as Store
      // 最长保留一年（演示）：超期则重置
      if (Date.now() - (obj.lastUpdated ?? 0) > 1000 * 60 * 60 * 24 * 365) {
        const seeded = seedChains()
        const trends = genAllTrends(seeded)
        const store: Store = { chains: seeded, trends, lastUpdated: Date.now() }
        save(store)
        return store
      }
      return obj
    } catch {}
  }
  const chains = seedChains()
  const trends = genAllTrends(chains)
  const store: Store = { chains, trends, lastUpdated: Date.now() }
  save(store)
  return store
}

function save(store: Store) {
  store.lastUpdated = Date.now()
  localStorage.setItem(STORE_KEY, JSON.stringify(store))
}

let cache: Store | null = null
function ensure(): Store {
  if (!cache) cache = load()
  return cache
}

function genAllTrends(chains: Chain[]) {
  const trends: Store["trends"] = {}
  chains.forEach((c) =>
    c.floors.forEach((f) =>
      f.rooms.forEach((r) => {
        trends[key(c.id, f.id, r.id)] = genTrend()
      })
    )
  )
  return trends
}

function key(chainId: string, floorId: string, roomId: string) {
  return `${chainId}-${floorId}-${roomId}`
}

function genTrend(): { temperature: TrendSeries[]; humidity: TrendSeries[]; presence: TrendSeries[] } {
  const now = Date.now()
  const step = 5 * 60 * 1000 // 5min
  const points = 288 // 24h
  const temperature: TrendSeries[] = []
  const humidity: TrendSeries[] = []
  const presence: TrendSeries[] = []
  let baseT = 23 + Math.random() * 4
  let baseH = 50 + Math.random() * 15
  for (let i = points - 1; i >= 0; i--) {
    const t = now - i * step
    baseT += (Math.random() - 0.5) * 0.3
    baseH += (Math.random() - 0.5) * 0.8
    const p = Math.max(0, Math.sin((i / points) * Math.PI * 2 * 2) * 60 + Math.random() * 20) // 波动存在强度
    temperature.push({ t, v: Number(baseT.toFixed(2)) })
    humidity.push({ t, v: Number(Math.min(80, Math.max(30, baseH))).toFixed ? { t, v: Number(Math.min(80, Math.max(30, baseH)).toFixed(2)) } as any : { t, v: Math.min(80, Math.max(30, baseH)) } )
    // 修正 humidity push
  }
  // 修正 humidity 循环（上面为了类型保守，单独重算）
  humidity.length = 0
  let baseH2 = 50 + Math.random() * 15
  for (let i = points - 1; i >= 0; i--) {
    const t = now - i * step
    baseH2 += (Math.random() - 0.5) * 0.8
    humidity.push({ t, v: Number(Math.min(80, Math.max(30, baseH2)).toFixed(2)) })
  }
  for (let i = points - 1; i >= 0; i--) {
    const t = now - i * step
    const v = Math.max(0, Math.sin((i / points) * Math.PI * 2 * 2) * 60 + Math.random() * 20)
    presence.push({ t, v: Number(v.toFixed(2)) })
  }
  return { temperature, humidity, presence }
}

// Public APIs
export function getAllChains(): Chain[] {
  return ensure().chains
}
export function getChainById(id: string): Chain | undefined {
  return ensure().chains.find((c) => c.id === id)
}
export function getRoom(chainId: string, floorId: string, roomId: string): Room {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  const f = c.floors.find((x) => x.id === floorId)!
  return f.rooms.find((x) => x.id === roomId)!
}
export function getRoomTrend(chainId: string, floorId: string, roomId: string) {
  const st = ensure()
  return st.trends[key(chainId, floorId, roomId)]
}
export function setCircuitState(chainId: string, floorId: string, roomId: string, circuitId: string, on: boolean) {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  const f = c.floors.find((x) => x.id === floorId)!
  const r = f.rooms.find((x) => x.id === roomId)!
  const ci = r.circuits.find((x) => x.id === circuitId)
  if (ci) ci.on = on
  save(st)
}
export function setCompressorPercent(chainId: string, floorId: string, roomId: string, percent: number) {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  const f = c.floors.find((x) => x.id === floorId)!
  const r = f.rooms.find((x) => x.id === roomId)!
  r.ac.compressorPercent = Math.max(0, Math.min(100, Math.round(percent)))
  save(st)
}
export function addFloor(chainId: string, name: string) {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  c.floors.push({ id: nanoid(6), name, rooms: [] })
  save(st)
}
export function addRoom(chainId: string, floorName: string, roomName: string) {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  let f = c.floors.find((x) => x.name === floorName)
  if (!f) {
    f = { id: nanoid(6), name: floorName, rooms: [] }
    c.floors.push(f)
  }
  const room: Room = {
    id: nanoid(6),
    name: roomName,
    description: "自动接入的放映室",
    circuits: [
      { id: "c1", name: "照明主回路", on: true },
      { id: "c2", name: "放映设备回路", on: true },
      { id: "c3", name: "通风回路", on: false },
    ],
    ac: { compressorPercent: 60 },
  }
  f.rooms.push(room)
  st.trends[key(c.id, f.id, room.id)] = genTrend()
  // 简单更新统计
  c.metrics.devicesOnline += 3
  save(st)
}
export function addScene(chainId: string, payload: { name: string; compressorPercent: number; circuitsOn: string[] }) {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  c.scenes.push({ id: nanoid(6), ...payload })
  save(st)
}
export function applyScene(chainId: string, sceneId: string) {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  const s = c.scenes.find((x) => x.id === sceneId)
  if (!s) return
  c.floors.forEach((f) =>
    f.rooms.forEach((r) => {
      r.ac.compressorPercent = s.compressorPercent
      r.circuits.forEach((ci) => {
        if (s.circuitsOn.includes(ci.id)) ci.on = true
      })
    })
  )
  save(st)
}
export function addAutomationRule(chainId: string, rule: Omit<AutomationRule, "id">) {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  c.automations.push({ id: nanoid(6), ...rule })
  save(st)
}
export function removeAutomationRule(chainId: string, ruleId: string) {
  const st = ensure()
  const c = st.chains.find((x) => x.id === chainId)!
  c.automations = c.automations.filter((r) => r.id !== ruleId)
  save(st)
}

// 简易前端自动化评估（每30s），仅示意
if (typeof window !== "undefined") {
  const runnerKey = "__automation_runner_once__"
  // 防止重复安装
  if (!(window as any)[runnerKey]) {
    ;(window as any)[runnerKey] = true
    setInterval(() => {
      const st = ensure()
      st.chains.forEach((c) => {
        c.automations.forEach((r) => {
          if (r.type === "temp-threshold") {
            c.floors.forEach((f) =>
              f.rooms.forEach((room) => {
                const ts = st.trends[key(c.id, f.id, room.id)].temperature
                const last5 = ts.slice(-5)
                const avg = last5.reduce((a, b) => a + b.v, 0) / Math.max(1, last5.length)
                if (avg > r.threshold && room.ac.compressorPercent < r.targetPct) {
                  room.ac.compressorPercent = r.targetPct
                }
              })
            )
          }
        })
      })
      save(st)
    }, 30_000)
  }
}
