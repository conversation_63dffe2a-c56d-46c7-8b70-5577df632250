"use client"

import { Tabs, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { getChainById } from "@/lib/db"

export function FloorSelector({
  chainId,
  value,
  onChange,
}: {
  chainId: string
  value: string | "all"
  onChange: (v: string | "all") => void
}) {
  const chain = getChainById(chainId)!
  return (
    <Tabs value={value} onValueChange={(v) => onChange(v as any)} className="w-full md:w-auto">
      <TabsList className="bg-white/5 border border-white/10">
        <TabsTrigger value="all">全部楼层</TabsTrigger>
        {chain.floors.map((f) => (
          <TabsTrigger key={f.id} value={f.id}>
            {f.name}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  )
}
