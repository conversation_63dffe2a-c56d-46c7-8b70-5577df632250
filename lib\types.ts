export type Circuit = {
  id: string
  name: string
  on: boolean
}

export type Room = {
  id: string
  name: string
  description?: string
  circuits: Circuit[]
  ac: {
    compressorPercent: number
  }
}

export type Floor = {
  id: string
  name: string
  rooms: Room[]
}

export type Scene = {
  id: string
  name: string
  // 简化示例：设压缩机目标百分比 + 指定回路打开
  compressorPercent: number
  circuitsOn: string[]
}

export type AutomationRule = {
  id: string
  name: string
  type: "temp-threshold"
  threshold: number
  targetPct: number
}

export type Chain = {
  id: string
  city: string
  name: string
  bannerImage?: string
  floors: Floor[]
  scenes: Scene[]
  automations: AutomationRule[]
  metrics: {
    devicesOnline: number
    devicesOffline: number
    thPoints: number
    presencePoints: number
  }
}

export type TrendSeries = { t: number; v: number }
