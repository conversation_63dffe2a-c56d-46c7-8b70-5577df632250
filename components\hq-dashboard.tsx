"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Activity, ArrowRight, Building2, Server, ThermometerSun, Waves } from 'lucide-react'
import Image from "next/image"
import { DefaultHQKPIs } from "./kpi-cards"
import type { Chain } from "@/lib/types"
import { cn } from "@/lib/utils"
import { useMemo } from "react"

export function HQDashboard({
  role,
  currentChainId,
  chains,
}: {
  role: "hq" | "chain"
  currentChainId?: string | null
  chains: Chain[]
}) {
  const visibleChains = useMemo(() => {
    if (role === "chain" && currentChainId) {
      return chains.filter((c) => c.id === currentChainId)
    }
    return chains
  }, [role, currentChainId, chains])

  const totals = useMemo(() => {
    const rooms = visibleChains.reduce((acc, c) => acc + c.floors.reduce((a, f) => a + f.rooms.length, 0), 0)
    const online = visibleChains.reduce(
      (acc, c) => acc + c.metrics.devicesOnline,
      0
    )
    const offline = visibleChains.reduce(
      (acc, c) => acc + c.metrics.devicesOffline,
      0
    )
    const automations = visibleChains.reduce((acc, c) => acc + c.automations.length, 0)
    return { rooms, online, offline, automations }
  }, [visibleChains])

  return (
    <div className="space-y-6">
      <DefaultHQKPIs
        chainsCount={visibleChains.length}
        roomsCount={totals.rooms}
        onlineDevices={totals.online}
        offlineDevices={totals.offline}
        activeAutomations={totals.automations}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {visibleChains.map((chain) => (
          <Card
            key={chain.id}
            className={cn(
              "overflow-hidden border-white/10 bg-gradient-to-b from-white/[0.06] to-white/[0.02]"
            )}
          >
            <CardHeader className="p-0">
              <div className="relative h-28 w-full">
                <Image
                  src={chain.bannerImage ?? "/images/cinema-banner.png"}
                  alt={`${chain.city}-${chain.name}`}
                  fill
                  sizes="(max-width:768px) 100vw, 33vw"
                  className="object-cover opacity-80"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#0b0f1a] via-transparent to-transparent" />
              </div>
            </CardHeader>
            <CardContent className="p-4">
              <div className="flex items-start justify-between gap-3">
                <div>
                  <CardTitle className="text-lg">
                    {chain.city} - {chain.name}{" "}
                    <Badge variant="outline" className="ml-2 bg-white/5 border-white/10 text-white">
                      {chain.floors.length}F / {chain.floors.reduce((a, f) => a + f.rooms.length, 0)} Rooms
                    </Badge>
                  </CardTitle>
                  <div className="text-xs text-white/60 mt-1">近24小时运行良好</div>
                </div>
                <div className="grid grid-cols-3 gap-2 text-center text-[11px]">
                  <MiniStat icon={Activity} label="在线" value={String(chain.metrics.devicesOnline)} accent="text-emerald-400" />
                  <MiniStat icon={ThermometerSun} label="温湿度点" value={String(chain.metrics.thPoints)} />
                  <MiniStat icon={Waves} label="存在感应" value={String(chain.metrics.presencePoints)} />
                </div>
              </div>
              <div className="mt-4 flex items-center gap-2">
                <Badge className="bg-emerald-500/15 text-emerald-300 border-emerald-500/30">稳定</Badge>
                <Badge variant="outline" className="bg-white/5 border-white/10 text-white">
                  支持场景 / 自动化
                </Badge>
                <Badge variant="outline" className="bg-white/5 border-white/10 text-white">
                  API 接入预留
                </Badge>
              </div>
              <div className="mt-4 flex gap-2">
                <Button asChild className="bg-emerald-600 hover:bg-emerald-500">
                  <Link href={`/cinema/${chain.id}`}>
                    进入院线大屏 <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                </Button>
                <Button variant="outline" className="border-white/10 bg-white/5 text-white hover:bg-white/10">
                  <Building2 className="w-4 h-4 mr-1" />
                  设备清单
                </Button>
                <Button variant="outline" className="border-white/10 bg-white/5 text-white hover:bg-white/10">
                  <Server className="w-4 h-4 mr-1" />
                  API 文档
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

function MiniStat({
  icon: Icon,
  label,
  value,
  accent,
}: {
  icon: any
  label: string
  value: string
  accent?: string
}) {
  return (
    <div className="bg-white/5 border border-white/10 rounded-md px-2 py-1">
      <div className="flex items-center justify-center gap-1">
        <Icon className={`w-3.5 h-3.5 ${accent ?? "text-white/70"}`} />
        <span className="tabular-nums">{value}</span>
      </div>
      <div className="text-white/60">{label}</div>
    </div>
  )
}
