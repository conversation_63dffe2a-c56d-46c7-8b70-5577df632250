"use client"

import { HQDashboard } from "@/components/hq-dashboard"
import TopBar from "@/components/top-bar"
import { useRole } from "@/hooks/use-role"
import { getAllChains } from "@/lib/db"

export default function Page() {
  const { role, currentChainId } = useRole()
  const chains = getAllChains()

  return (
    <main className="min-h-screen bg-[#0b0f1a] text-white">
      <TopBar title="影院智控大屏看板 · 总台" subtitle="全国院线智能设备运行总览 / 实时监控与调度" />
      <div className="mx-auto max-w-[1600px] px-4 pb-12">
        <HQDashboard role={role} currentChainId={currentChainId} chains={chains} />
      </div>
    </main>
  )
}
