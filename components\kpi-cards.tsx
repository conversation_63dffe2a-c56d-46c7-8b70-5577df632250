"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Activity, AlarmSmoke, Building2, Cpu, Server } from 'lucide-react'

type KPI = {
  label: string
  value: string
  delta?: string
  icon: any
}

export function KPICards({ items }: { items: KPI[] }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-3">
      {items.map((k, i) => (
        <Card key={i} className="bg-gradient-to-b from-white/[0.06] to-white/[0.02] border-white/10">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-emerald-500/10 border border-emerald-500/20 flex items-center justify-center text-emerald-400">
                <k.icon className="w-5 h-5" />
              </div>
              <div className="text-sm text-white/70">{k.label}</div>
            </div>
            <div className="mt-3 text-2xl font-semibold tracking-tight">{k.value}</div>
            {k.delta ? <div className="text-xs text-emerald-400 mt-1">{k.delta}</div> : null}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export const DefaultHQKPIs = (props: {
  chainsCount: number
  roomsCount: number
  onlineDevices: number
  offlineDevices: number
  activeAutomations: number
}) => {
  const items: KPI[] = [
    { label: "院线数", value: String(props.chainsCount), icon: Building2 },
    { label: "放映室数", value: String(props.roomsCount), icon: Server },
    { label: "设备在线", value: String(props.onlineDevices), delta: "+1.2% 24h", icon: Activity },
    { label: "设备离线", value: String(props.offlineDevices), icon: AlarmSmoke },
    { label: "自动化活跃", value: String(props.activeAutomations), delta: "稳定", icon: Cpu },
  ]
  return <KPICards items={items} />
}
