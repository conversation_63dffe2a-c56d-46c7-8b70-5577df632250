"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, PanelsTopLeft, Shield } from 'lucide-react'
import Link from "next/link"
import RoleSwitcher from "./role-switcher"
import Image from "next/image"

type Props = {
  title: string
  subtitle?: string
  backHref?: string
}

export default function TopBar({ title, subtitle, backHref }: Props) {
  return (
    <header className="sticky top-0 z-40 border-b border-white/10 backdrop-blur supports-[backdrop-filter]:bg-[#0b0f1a]/70">
      <div className="mx-auto max-w-[1800px] px-4 h-16 flex items-center gap-3">
        {backHref ? (
          <Button asChild size="icon" variant="ghost" className="text-white">
            <Link href={backHref} aria-label="返回">
              <ChevronLeft className="w-5 h-5" />
            </Link>
          </Button>
        ) : (
          <div className="w-10 h-10 relative rounded-md overflow-hidden ring-1 ring-white/10">
            <Image
              src="/images/cinema-hero.png"
              alt="影院智控"
              fill
              className="object-cover"
              sizes="40px"
              priority
            />
          </div>
        )}

        <div className="flex-1">
          <div className="text-lg md:text-2xl font-semibold tracking-tight flex items-center gap-2">
            <PanelsTopLeft className="w-5 h-5 text-emerald-400" />
            {title}
            <span className="ml-2 hidden md:inline-flex items-center gap-1 text-xs rounded bg-white/5 px-2 py-0.5 ring-1 ring-white/10">
              <Shield className="w-3 h-3 text-emerald-400" />
              {"长期稳定 · 可扩展"}
            </span>
          </div>
          {subtitle ? (
            <div className="text-xs md:text-sm text-white/60 mt-0.5">{subtitle}</div>
          ) : null}
        </div>

        <RoleSwitcher />
      </div>
    </header>
  )
}
