"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Separator } from "@/components/ui/separator"
import { useEffect, useMemo, useState, useTransition } from "react"
import { getRoom, setCircuitState, setCompressorPercent, getRoomTrend } from "@/lib/db"
import { TrendChart } from "./trend-chart"
import { Gauge } from 'lucide-react'
import { cn } from "@/lib/utils"

export function RoomCard({ chainId, floorId, roomId }: { chainId: string; floorId: string; roomId: string }) {
  const room = getRoom(chainId, floorId, roomId)
  const [pct, setPct] = useState(room.ac.compressorPercent)
  const [isPending, startTransition] = useTransition()

  useEffect(() => {
    setPct(room.ac.compressorPercent)
  }, [room.ac.compressorPercent])

  const trend = useMemo(() => getRoomTrend(chainId, floorId, roomId), [chainId, floorId, roomId])

  return (
    <Card className="bg-gradient-to-b from-white/[0.06] to-white/[0.02] border-white/10">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-2">
          <div>
            <CardTitle className="text-base">{room.name}</CardTitle>
            <div className="text-xs text-white/60">{room.description ?? "数字放映室 · 智能控制"}</div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={cn("border-emerald-500/30 text-emerald-300 bg-emerald-500/10")}>在线</Badge>
            <Badge variant="outline" className="border-white/10 bg-white/5 text-white">
              {room.circuits.length} 回路
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 回路开关 */}
        <section>
          <div className="text-sm font-medium mb-2">回路控制</div>
          <div className="grid grid-cols-2 gap-2">
            {room.circuits.map((c) => (
              <div
                key={c.id}
                className="flex items-center justify-between rounded-md bg-white/5 border border-white/10 px-3 py-2"
              >
                <div className="text-sm">{c.name}</div>
                <Switch
                  checked={c.on}
                  onCheckedChange={(checked) =>
                    startTransition(() => setCircuitState(chainId, floorId, roomId, c.id, checked))
                  }
                  aria-label={`回路 ${c.name} 开关`}
                />
              </div>
            ))}
          </div>
        </section>

        <Separator className="bg-white/10" />

        {/* 压缩机百分比 */}
        <section>
          <div className="text-sm font-medium mb-2 flex items-center gap-2">
            <Gauge className="w-4 h-4 text-emerald-400" />
            变频空调压缩机百分比
            <span className="ml-auto text-xs text-white/60">{pct}%</span>
          </div>
          <Slider
            value={[pct]}
            min={0}
            max={100}
            step={1}
            onValueChange={(v) => setPct(v[0] ?? 0)}
            onValueCommit={(v) => startTransition(() => setCompressorPercent(chainId, floorId, roomId, v[0] ?? 0))}
            className="[&>span:first-child]:bg-emerald-500/30 [&_[role=slider]]:bg-emerald-400"
          />
          {isPending ? <div className="text-xs text-white/60 mt-1">应用中...</div> : null}
        </section>

        <Separator className="bg-white/10" />

        {/* 曲线区：温湿度 与 存在感应 */}
        <section className="grid grid-cols-1 xl:grid-cols-2 gap-3">
          <div className="rounded-md bg-white/5 border border-white/10 p-2">
            <div className="text-sm mb-1">温湿度曲线（近24h，5min粒度）</div>
            <TrendChart
              height={180}
              series={[
                { name: "温度 ℃", color: "#34d399", data: trend.temperature },
                { name: "湿度 %", color: "#60a5fa", data: trend.humidity },
              ]}
            />
          </div>
          <div className="rounded-md bg-white/5 border border-white/10 p-2">
            <div className="text-sm mb-1">存在感应强度曲线（近24h）</div>
            <TrendChart height={180} series={[{ name: "存在强度", color: "#f59e0b", data: trend.presence }]} />
          </div>
        </section>
      </CardContent>
    </Card>
  )
}
