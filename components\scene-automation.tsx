"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { getChainById, addScene, applyScene, addAutomationRule, removeAutomationRule } from "@/lib/db"
import { CopyPlus, PlayCircle, Sparkles, Trash2 } from 'lucide-react'

export function SceneAutomation({ chainId }: { chainId: string }) {
  const chain = getChainById(chainId)!
  const [openScene, setOpenScene] = useState(false)
  const [openRule, setOpenRule] = useState(false)
  const [sceneName, setSceneName] = useState("")
  const [ruleName, setRuleName] = useState("")
  const [threshold, setThreshold] = useState(28)
  const [targetPct, setTargetPct] = useState(70)

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Badge className="bg-emerald-500/15 text-emerald-300 border-emerald-500/30">场景与自动化</Badge>
        <div className="text-sm text-white/70">按需创建场景与规则，统一应用至院线或楼层/放映室</div>
        <div className="ml-auto flex gap-2">
          <Dialog open={openScene} onOpenChange={setOpenScene}>
            <DialogTrigger asChild>
              <Button className="bg-emerald-600 hover:bg-emerald-500">
                <CopyPlus className="w-4 h-4 mr-1" />
                新建场景
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-[#0e1424] text-white border-white/10">
              <DialogHeader>
                <DialogTitle>新建场景</DialogTitle>
              </DialogHeader>
              <div className="grid gap-3">
                <div className="space-y-2">
                  <Label>场景名称</Label>
                  <Input
                    value={sceneName}
                    onChange={(e) => setSceneName(e.target.value)}
                    placeholder="例如：上座高峰、闭馆节能"
                    className="bg-white/5 border-white/10 text-white placeholder:text-white/40"
                  />
                </div>
                <div className="text-xs text-white/60">
                  当前版本示例：将空调压缩机百分比设为指定值，回路1/2 打开。可后续扩展更多实体动作。
                </div>
              </div>
              <DialogFooter>
                <Button
                  onClick={() => {
                    if (sceneName.trim()) {
                      addScene(chainId, { name: sceneName.trim(), compressorPercent: 75, circuitsOn: ["c1", "c2"] })
                      setSceneName("")
                      setOpenScene(false)
                    }
                  }}
                  className="bg-emerald-600 hover:bg-emerald-500"
                >
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={openRule} onOpenChange={setOpenRule}>
            <DialogTrigger asChild>
              <Button variant="outline" className="border-white/10 bg-white/5 text-white hover:bg-white/10">
                <Sparkles className="w-4 h-4 mr-1" />
                新建自动化
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-[#0e1424] text-white border-white/10">
              <DialogHeader>
                <DialogTitle>新建自动化规则</DialogTitle>
              </DialogHeader>
              <div className="grid gap-3">
                <div className="space-y-2">
                  <Label>规则名称</Label>
                  <Input
                    value={ruleName}
                    onChange={(e) => setRuleName(e.target.value)}
                    placeholder="例如：高温提速"
                    className="bg-white/5 border-white/10 text-white placeholder:text-white/40"
                  />
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label>温度阈值 ℃</Label>
                    <Input
                      type="number"
                      value={threshold}
                      onChange={(e) => setThreshold(Number(e.target.value))}
                      className="bg-white/5 border-white/10 text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>压缩机目标 %</Label>
                    <Input
                      type="number"
                      value={targetPct}
                      onChange={(e) => setTargetPct(Number(e.target.value))}
                      className="bg-white/5 border-white/10 text-white"
                    />
                  </div>
                </div>
                <div className="text-xs text-white/60">
                  示例：若近5分钟平均温度大于阈值，则将压缩机提升至目标百分比（演示逻辑在前端定时器内评估）。
                </div>
              </div>
              <DialogFooter>
                <Button
                  onClick={() => {
                    if (ruleName.trim()) {
                      addAutomationRule(chainId, { name: ruleName.trim(), type: "temp-threshold", threshold, targetPct })
                      setOpenRule(false)
                      setRuleName("")
                    }
                  }}
                  className="bg-emerald-600 hover:bg-emerald-500"
                >
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Separator className="bg-white/10" />

      <div className="grid gap-3">
        <div className="text-sm text-white/80">场景（{chain.scenes.length}）</div>
        <div className="flex flex-wrap gap-2">
          {chain.scenes.map((s) => (
            <Button
              key={s.id}
              variant="outline"
              className="border-white/10 bg-white/5 text-white hover:bg-white/10"
              onClick={() => applyScene(chainId, s.id)}
            >
              <PlayCircle className="w-4 h-4 mr-1 text-emerald-400" />
              {s.name}
            </Button>
          ))}
          {chain.scenes.length === 0 ? <div className="text-xs text-white/50">暂无场景</div> : null}
        </div>
      </div>

      <Separator className="bg-white/10" />

      <div className="grid gap-3">
        <div className="text-sm text-white/80">自动化规则（{chain.automations.length}）</div>
        <div className="grid md:grid-cols-2 gap-2">
          {chain.automations.map((r) => (
            <div key={r.id} className="rounded-md bg-white/5 border border-white/10 p-3">
              <div className="flex items-center gap-2">
                <Badge className="bg-emerald-500/15 text-emerald-300 border-emerald-500/30">启用</Badge>
                <div className="text-sm font-medium">{r.name}</div>
                <div className="ml-auto">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="text-white/70 hover:text-white"
                    onClick={() => removeAutomationRule(chainId, r.id)}
                    aria-label="删除规则"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="text-xs text-white/60 mt-1">
                规则类型：{r.type === "temp-threshold" ? "温度阈值触发" : r.type}
                {" · 阈值 "}{r.threshold}℃
                {" · 目标 "}{r.targetPct}%
              </div>
            </div>
          ))}
          {chain.automations.length === 0 ? <div className="text-xs text-white/50">暂无自动化规则</div> : null}
        </div>
      </div>

      <div className="text-xs text-white/40">
        注：协议（TCP/UDP/RS485）与 API 对接需由后端网关/设备接入层实现，本前端已预留实体与动作模型、场景/规则 UI 与数据结构。
      </div>
    </div>
  )
}
