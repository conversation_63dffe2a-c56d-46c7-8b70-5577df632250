"use client"

import { useMemo, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Plus, Settings2, SlidersHorizontal } from 'lucide-react'
import { getChainById, addFloor, addRoom } from "@/lib/db"
import { FloorSelector } from "./floor-selector"
import { RoomCard } from "./room-card"
import { SceneAutomation } from "./scene-automation"
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function ChainDashboard({ chainId }: { chainId: string }) {
  const [activeFloorId, setActiveFloorId] = useState<string | "all">("all")
  const chain = getChainById(chainId)!
  const rooms = useMemo(() => {
    if (activeFloorId === "all") {
      return chain.floors.flatMap((f) => f.rooms.map((r) => ({ ...r, floorId: f.id, floorName: f.name })))
    }
    const f = chain.floors.find((x) => x.id === activeFloorId)
    return f ? f.rooms.map((r) => ({ ...r, floorId: f.id, floorName: f.name })) : []
  }, [chain, activeFloorId])

  return (
    <div className="space-y-6">
      <div className="flex flex-wrap items-center gap-2">
        <FloorSelector chainId={chainId} value={activeFloorId} onChange={setActiveFloorId} />
        <AddFloorDialog chainId={chainId} />
        <AddRoomDialog chainId={chainId} />
        <Button variant="outline" className="ml-auto border-white/10 bg-white/5 text-white hover:bg-white/10">
          <Settings2 className="w-4 h-4 mr-1" />
          协议与网关对接（预留）
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {rooms.map((room) => (
          <RoomCard key={room.id} chainId={chainId} floorId={room.floorId} roomId={room.id} />
        ))}
      </div>

      <Card className="bg-gradient-to-b from-white/[0.06] to-white/[0.02] border-white/10">
        <CardContent className="p-4">
          <SceneAutomation chainId={chainId} />
        </CardContent>
      </Card>
    </div>
  )
}

function AddFloorDialog({ chainId }: { chainId: string }) {
  const [name, setName] = useState("")
  const [open, setOpen] = useState(false)
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-emerald-600 hover:bg-emerald-500">
          <Plus className="w-4 h-4 mr-1" />
          添加楼层
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-[#0e1424] text-white border-white/10">
        <DialogHeader>
          <DialogTitle>添加楼层</DialogTitle>
        </DialogHeader>
        <div className="space-y-2">
          <Label>楼层名称</Label>
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="例如：F3 / 放映层"
            className="bg-white/5 border-white/10 text-white placeholder:text-white/40"
          />
        </div>
        <DialogFooter>
          <Button
            onClick={() => {
              if (name.trim()) {
                addFloor(chainId, name.trim())
                setOpen(false)
                setName("")
              }
            }}
            className="bg-emerald-600 hover:bg-emerald-500"
          >
            保存
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

function AddRoomDialog({ chainId }: { chainId: string }) {
  const [open, setOpen] = useState(false)
  const [name, setName] = useState("")
  const [floorName, setFloorName] = useState("")
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="border-white/10 bg-white/5 text-white hover:bg-white/10">
          <SlidersHorizontal className="w-4 h-4 mr-1" />
          添加放映室
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-[#0e1424] text-white border-white/10">
        <DialogHeader>
          <DialogTitle>添加放映室</DialogTitle>
        </DialogHeader>
        <div className="grid gap-3">
          <div className="space-y-2">
            <Label>楼层（没有则自动创建）</Label>
            <Input
              value={floorName}
              onChange={(e) => setFloorName(e.target.value)}
              placeholder="例如：F2"
              className="bg-white/5 border-white/10 text-white placeholder:text-white/40"
            />
          </div>
          <div className="space-y-2">
            <Label>放映室名称</Label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="例如：2号厅 / IMAX厅"
              className="bg-white/5 border-white/10 text-white placeholder:text-white/40"
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            onClick={() => {
              if (floorName.trim() && name.trim()) {
                addRoom(chainId, floorName.trim(), name.trim())
                setOpen(false)
                setName("")
                setFloorName("")
              }
            }}
            className="bg-emerald-600 hover:bg-emerald-500"
          >
            保存
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
