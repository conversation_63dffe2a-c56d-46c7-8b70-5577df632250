"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
} from "recharts"

type Series = {
  name: string
  color: string
  data: { t: number; v: number }[]
}

export function TrendChart({ series, height = 160 }: { series: Series[]; height?: number }) {
  // 合并对齐：假设 t 一致
  const data = (series[0]?.data ?? []).map((pt, idx) => {
    const row: any = { t: pt.t }
    series.forEach((s, si) => {
      row[`s${si}`] = s.data[idx]?.v ?? null
    })
    return row
  })

  const tsFmt = (t: number) => {
    const d = new Date(t)
    return `${d.getHours().toString().padStart(2, "0")}:${d.getMinutes().toString().padStart(2, "0")}`
  }

  return (
    <div style={{ width: "100%", height }}>
      <ResponsiveContainer>
        <Line<PERSON>hart data={data} margin={{ top: 8, right: 12, bottom: 0, left: 0 }}>
          <CartesianGrid stroke="rgba(255,255,255,0.08)" strokeDasharray="3 3" />
          <XAxis
            dataKey="t"
            tickFormatter={tsFmt}
            stroke="rgba(255,255,255,0.6)"
            tick={{ fontSize: 11 }}
          />
          <YAxis stroke="rgba(255,255,255,0.6)" tick={{ fontSize: 11 }} />
          <Tooltip
            contentStyle={{
              background: "rgba(15,23,42,0.9)",
              border: "1px solid rgba(255,255,255,0.1)",
              borderRadius: 8,
              color: "#fff",
              fontSize: 12,
            }}
            labelFormatter={(label) => tsFmt(Number(label))}
            formatter={(value, name, props) => {
              const si = Number(String(props.dataKey).replace("s", ""))
              return [value as any, series[si].name]
            }}
          />
          {series.map((s, si) => (
            <Line
              key={si}
              dataKey={`s${si}`}
              name={s.name}
              type="monotone"
              stroke={s.color}
              strokeWidth={2}
              dot={false}
              isAnimationActive={false}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
