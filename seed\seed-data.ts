import type { Chain, Floor, Room } from "@/lib/types"
import { nanoid } from "nanoid"

function makeRoom(name: string): Room {
  return {
    id: nanoid(6),
    name,
    description: "标准银幕放映室",
    circuits: [
      { id: "c1", name: "照明主回路", on: true },
      { id: "c2", name: "放映设备回路", on: true },
      { id: "c3", name: "空调风机回路", on: false },
      { id: "c4", name: "应急照明回路", on: true },
    ],
    ac: { compressorPercent: Math.floor(40 + Math.random() * 40) },
  }
}

function makeFloor(name: string, rooms: string[]): Floor {
  return {
    id: nanoid(6),
    name,
    rooms: rooms.map(makeRoom),
  }
}

export function seedChains(): Chain[] {
  return [
    {
      id: "bj-wanda",
      city: "北京",
      name: "万达影城中关村店",
      bannerImage: "/images/cinema-banner.png",
      floors: [
        makeFloor("F3", ["1号厅", "2号厅", "3号厅"]),
        makeFloor("F4", ["4号厅", "5号厅 IMAX", "6号厅"]),
      ],
      scenes: [
        { id: nanoid(6), name: "上座高峰", compressorPercent: 80, circuitsOn: ["c1", "c2"] },
        { id: nanoid(6), name: "闭馆节能", compressorPercent: 30, circuitsOn: ["c4"] },
      ],
      automations: [{ id: nanoid(6), name: "高温提速", type: "temp-threshold", threshold: 28, targetPct: 75 }],
      metrics: { devicesOnline: 68, devicesOffline: 2, thPoints: 24, presencePoints: 12 },
    },
    {
      id: "sh-dadi",
      city: "上海",
      name: "大地影院五角场店",
      bannerImage: "/images/cinema-banner-2.png",
      floors: [makeFloor("F2", ["1号厅", "2号厅", "3号厅 杜比全景声"]), makeFloor("F3", ["4号厅", "5号厅"])],
      scenes: [{ id: nanoid(6), name: "周末扩容", compressorPercent: 70, circuitsOn: ["c1", "c2", "c3"] }],
      automations: [],
      metrics: { devicesOnline: 47, devicesOffline: 1, thPoints: 18, presencePoints: 9 },
    },
  ]
}
