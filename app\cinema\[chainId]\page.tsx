"use client"

import { notFound, useParams } from "next/navigation"
import TopBar from "@/components/top-bar"
import { ChainDashboard } from "@/components/chain-dashboard"
import { getChainById } from "@/lib/db"
import { useRole } from "@/hooks/use-role"

export default function ChainPage() {
  const params = useParams<{ chainId: string }>()
  const chain = getChainById(params.chainId)
  const { role, currentChainId } = useRole()

  if (!chain) return notFound()

  if (role === "chain" && currentChainId && currentChainId !== chain.id) {
    // 简单前端权限：院线账号只能访问自身院线
    return (
      <main className="min-h-screen bg-[#0b0f1a] text-white grid place-items-center p-6">
        <div className="text-center space-y-2">
          <div className="text-2xl font-semibold">无权限访问该院线</div>
          <div className="text-sm text-muted-foreground">请切换至您所属的院线账号或联系管理员</div>
        </div>
      </main>
    )
  }

  return (
    <main className="min-h-screen bg-[#0b0f1a] text-white">
      <TopBar
        title={`${chain.city} - ${chain.name}`}
        subtitle="院线大屏 · 楼层/放映室/设备/场景/自动化"
        backHref="/"
      />
      <div className="mx-auto max-w-[1800px] px-4 pb-12">
        <ChainDashboard chainId={chain.id} />
      </div>
    </main>
  )
}
