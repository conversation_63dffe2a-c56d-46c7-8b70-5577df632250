"use client"

import { useEffect, useState } from "react"

type Role = "hq" | "chain"

const KEY = "cinema_role"
const KEY_CHAIN = "cinema_role_chainId"

export function useRole() {
  const [role, setRoleState] = useState<Role>("hq")
  const [currentChainId, setChainIdState] = useState<string | null>(null)

  useEffect(() => {
    const r = (localStorage.getItem(KEY) as Role) ?? "hq"
    const id = localStorage.getItem(KEY_CHAIN)
    setRoleState(r)
    setChainIdState(id)
  }, [])

  function setRole(r: Role) {
    setRoleState(r)
    localStorage.setItem(KEY, r)
  }
  function setCurrentChainId(id: string | null) {
    setChainIdState(id)
    if (id) localStorage.setItem(KEY_CHAIN, id)
    else localStorage.removeItem(KEY_CHAIN)
  }
  return { role, setRole, currentChainId, setCurrent<PERSON>hainId }
}
